import pandas as pd
import numpy as np
import xgboost as xgb
from sklearn.model_selection import train_test_split, cross_val_score, KFold
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import seaborn as sns
import json
import warnings
warnings.filterwarnings('ignore')

class XGBoostTrainer:
    def __init__(self):
        self.model = None
        self.scaler = StandardScaler()
        self.feature_importance = None
        self.cv_scores = None
        
    def load_data(self):
        """Optimized feature'ları yükler"""
        print("Veriler yükleniyor...")
        
        train_features = pd.read_csv('train_features_optimized.csv', index_col=0)
        test_features = pd.read_csv('test_features_optimized.csv', index_col=0)
        
        print(f"Train features: {train_features.shape}")
        print(f"Test features: {test_features.shape}")
        
        return train_features, test_features
    
    def prepare_data(self, train_features, target_col='session_value'):
        """Veriyi model için hazırlar"""
        print("Veri hazırlanıyor...")
        
        # Target'ı ayır
        y = train_features[target_col].values
        
        # Feature'ları seç (numerik olanlar)
        numeric_cols = train_features.select_dtypes(include=[np.number]).columns.tolist()
        if target_col in numeric_cols:
            numeric_cols.remove(target_col)
        
        X = train_features[numeric_cols].values
        
        print(f"Feature sayısı: {X.shape[1]}")
        print(f"Sample sayısı: {X.shape[0]}")
        print(f"Target range: {y.min():.2f} - {y.max():.2f}")
        
        return X, y, numeric_cols
    
    def train_xgboost_baseline(self, X, y, test_size=0.2, random_state=42):
        """Baseline XGBoost modeli eğitir"""
        print("\n=== BASELINE XGBOOST EĞİTİMİ ===")
        
        # Train-validation split
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=test_size, random_state=random_state
        )
        
        print(f"Train: {X_train.shape}, Validation: {X_val.shape}")
        
        # Baseline parametreler
        baseline_params = {
            'objective': 'reg:squarederror',
            'eval_metric': 'rmse',
            'max_depth': 6,
            'learning_rate': 0.1,
            'n_estimators': 1000,
            'subsample': 0.8,
            'colsample_bytree': 0.8,
            'random_state': random_state,
            'n_jobs': -1
        }
        
        # Model eğit
        self.model = xgb.XGBRegressor(**baseline_params)
        
        # Early stopping ile eğit
        self.model.fit(
            X_train, y_train,
            eval_set=[(X_train, y_train), (X_val, y_val)],
            verbose=100
        )
        
        # Tahminler
        train_pred = self.model.predict(X_train)
        val_pred = self.model.predict(X_val)
        
        # Metrikleri hesapla
        train_mse = mean_squared_error(y_train, train_pred)
        val_mse = mean_squared_error(y_val, val_pred)
        train_rmse = np.sqrt(train_mse)
        val_rmse = np.sqrt(val_mse)
        train_mae = mean_absolute_error(y_train, train_pred)
        val_mae = mean_absolute_error(y_val, val_pred)
        train_r2 = r2_score(y_train, train_pred)
        val_r2 = r2_score(y_val, val_pred)

        print(f"\n=== BASELINE SONUÇLAR ===")
        print(f"Train MSE: {train_mse:.4f}")
        print(f"Val MSE: {val_mse:.4f}")
        print(f"Train RMSE: {train_rmse:.4f}")
        print(f"Val RMSE: {val_rmse:.4f}")
        print(f"Train MAE: {train_mae:.4f}")
        print(f"Val MAE: {val_mae:.4f}")
        print(f"Train R²: {train_r2:.4f}")
        print(f"Val R²: {val_r2:.4f}")

        return {
            'train_mse': train_mse,
            'val_mse': val_mse,
            'train_rmse': train_rmse,
            'val_rmse': val_rmse,
            'train_mae': train_mae,
            'val_mae': val_mae,
            'train_r2': train_r2,
            'val_r2': val_r2
        }
    
    def train_xgboost_optimized(self, X, y, random_state=42):
        """Optimized XGBoost modeli eğitir"""
        print("\n=== OPTIMIZED XGBOOST EĞİTİMİ ===")
        
        # Optimized parametreler (tabular data için best practices)
        optimized_params = {
            'objective': 'reg:squarederror',
            'eval_metric': 'rmse',
            'max_depth': 8,
            'learning_rate': 0.05,
            'n_estimators': 2000,
            'subsample': 0.85,
            'colsample_bytree': 0.85,
            'colsample_bylevel': 0.85,
            'min_child_weight': 3,
            'reg_alpha': 0.1,
            'reg_lambda': 1.0,
            'random_state': random_state,
            'n_jobs': -1
        }
        
        # Cross-validation ile değerlendir
        print("Cross-validation yapılıyor...")
        kfold = KFold(n_splits=5, shuffle=True, random_state=random_state)

        # Temporary model for CV
        temp_model = xgb.XGBRegressor(**optimized_params)
        cv_scores = cross_val_score(temp_model, X, y, cv=kfold,
                                   scoring='neg_mean_squared_error', n_jobs=-1)

        self.cv_scores = -cv_scores  # Pozitif yap

        print(f"CV MSE: {self.cv_scores.mean():.4f} (+/- {self.cv_scores.std() * 2:.4f})")
        
        # Final model eğit (tüm veri ile)
        print("Final model eğitiliyor...")
        
        # Train-validation split for early stopping
        X_train, X_val, y_train, y_val = train_test_split(
            X, y, test_size=0.15, random_state=random_state
        )
        
        self.model = xgb.XGBRegressor(**optimized_params)
        
        self.model.fit(
            X_train, y_train,
            eval_set=[(X_train, y_train), (X_val, y_val)],
            verbose=200
        )
        
        # Final değerlendirme
        train_pred = self.model.predict(X_train)
        val_pred = self.model.predict(X_val)

        train_mse = mean_squared_error(y_train, train_pred)
        val_mse = mean_squared_error(y_val, val_pred)
        train_rmse = np.sqrt(train_mse)
        val_rmse = np.sqrt(val_mse)
        train_r2 = r2_score(y_train, train_pred)
        val_r2 = r2_score(y_val, val_pred)

        print(f"\n=== OPTIMIZED SONUÇLAR ===")
        print(f"CV MSE: {self.cv_scores.mean():.4f}")
        print(f"Train MSE: {train_mse:.4f}")
        print(f"Val MSE: {val_mse:.4f}")
        print(f"Train RMSE: {train_rmse:.4f}")
        print(f"Val RMSE: {val_rmse:.4f}")
        print(f"Train R²: {train_r2:.4f}")
        print(f"Val R²: {val_r2:.4f}")

        return {
            'cv_mse_mean': self.cv_scores.mean(),
            'cv_mse_std': self.cv_scores.std(),
            'train_mse': train_mse,
            'val_mse': val_mse,
            'train_rmse': train_rmse,
            'val_rmse': val_rmse,
            'train_r2': train_r2,
            'val_r2': val_r2
        }
    
    def analyze_feature_importance(self, feature_names, top_k=20):
        """Feature importance analizi"""
        print(f"\n=== FEATURE IMPORTANCE ANALİZİ ===")
        
        if self.model is None:
            print("Model henüz eğitilmemiş!")
            return
        
        # Feature importance'ları al
        importance_gain = self.model.feature_importances_
        importance_df = pd.DataFrame({
            'feature': feature_names,
            'importance': importance_gain
        }).sort_values('importance', ascending=False)
        
        self.feature_importance = importance_df
        
        print(f"En önemli {top_k} feature:")
        print(importance_df.head(top_k))
        
        # Görselleştirme
        plt.figure(figsize=(12, 8))
        top_features = importance_df.head(top_k)
        
        plt.barh(range(len(top_features)), top_features['importance'])
        plt.yticks(range(len(top_features)), top_features['feature'])
        plt.xlabel('Feature Importance')
        plt.title(f'Top {top_k} XGBoost Feature Importance')
        plt.gca().invert_yaxis()
        plt.tight_layout()
        plt.savefig('xgboost_feature_importance.png', dpi=300, bbox_inches='tight')
        print("Feature importance grafiği 'xgboost_feature_importance.png' olarak kaydedildi.")
        
        return importance_df
    
    def make_predictions(self, test_features, feature_names):
        """Test verisi için tahmin yapar"""
        print("\n=== TEST TAHMİNLERİ ===")
        
        if self.model is None:
            print("Model henüz eğitilmemiş!")
            return None
        
        # Test feature'ları hazırla
        X_test = test_features[feature_names].values
        
        print(f"Test shape: {X_test.shape}")
        
        # Tahminleri yap
        test_predictions = self.model.predict(X_test)
        
        print(f"Tahmin range: {test_predictions.min():.2f} - {test_predictions.max():.2f}")
        print(f"Tahmin mean: {test_predictions.mean():.2f}")
        
        return test_predictions
    
    def create_submission(self, test_features, predictions, filename='xgboost_submission.csv'):
        """Submission dosyası oluşturur"""
        print(f"\n=== SUBMISSION OLUŞTURULUYOR ===")
        
        # Tahminleri orijinal session_value scale'ine çevir
        # Transformed target'ı kullandığımız için inverse transform gerekli

        # Önce outlier handling'den önceki scale'e çevir
        # Train'de session_value_transformed kullandık, bu log+capped değerdi

        # Basit yaklaşım: Tahminleri train range'ine sınırla
        train_features_check = pd.read_csv('train_features_optimized.csv', index_col=0)
        original_min = train_features_check['session_value'].min()
        original_max = train_features_check['session_value'].max()
        original_mean = train_features_check['session_value'].mean()

        # Tahminleri makul aralığa getir
        predictions_clipped = np.clip(predictions, original_min, original_max)
        predictions_original_scale = predictions_clipped
        
        # Submission dataframe oluştur
        submission = pd.DataFrame({
            'user_session': test_features.index,
            'session_value': predictions_original_scale
        })
        
        # Negatif değerleri ve aşırı büyük değerleri düzelt
        submission['session_value'] = np.clip(submission['session_value'], 5.38, 2500.0)
        
        submission.to_csv(filename, index=False)
        
        print(f"Submission kaydedildi: {filename}")
        print(f"Submission shape: {submission.shape}")
        print(f"Session value range: {submission['session_value'].min():.2f} - {submission['session_value'].max():.2f}")
        
        return submission

def main():
    print("XGBoost Training Pipeline başlıyor...")
    
    # Trainer oluştur
    trainer = XGBoostTrainer()
    
    # Veriyi yükle
    train_features, test_features = trainer.load_data()
    
    # Veriyi hazırla
    X, y, feature_names = trainer.prepare_data(train_features)
    
    # Baseline model eğit
    baseline_results = trainer.train_xgboost_baseline(X, y)
    
    # Optimized model eğit
    optimized_results = trainer.train_xgboost_optimized(X, y)
    
    # Feature importance analizi
    importance_df = trainer.analyze_feature_importance(feature_names)
    
    # Test tahminleri
    test_predictions = trainer.make_predictions(test_features, feature_names)
    
    # Submission oluştur
    submission = trainer.create_submission(test_features, test_predictions)
    
    # Sonuçları kaydet
    results = {
        'baseline': baseline_results,
        'optimized': optimized_results,
        'cv_scores': trainer.cv_scores.tolist() if trainer.cv_scores is not None else None
    }
    
    with open('xgboost_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    # Feature importance kaydet
    importance_df.to_csv('xgboost_feature_importance.csv', index=False)
    
    print(f"\n=== PIPELINE TAMAMLANDI ===")
    print(f"✅ Model eğitildi")
    print(f"✅ Feature importance analizi yapıldı")
    print(f"✅ Test tahminleri oluşturuldu")
    print(f"✅ Submission hazırlandı: xgboost_submission.csv")
    print(f"✅ Sonuçlar kaydedildi: xgboost_results.json")
    
    return trainer, submission

if __name__ == "__main__":
    trainer, submission = main()

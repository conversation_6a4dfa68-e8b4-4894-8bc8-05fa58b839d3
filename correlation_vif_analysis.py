import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from statsmodels.stats.outliers_influence import variance_inflation_factor
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

def calculate_vif(df, feature_cols):
    """VIF değerlerini hesaplar"""
    # Standardize et
    scaler = StandardScaler()
    df_scaled = pd.DataFrame(scaler.fit_transform(df[feature_cols]), 
                            columns=feature_cols, index=df.index)
    
    vif_data = pd.DataFrame()
    vif_data["Feature"] = feature_cols
    vif_data["VIF"] = [variance_inflation_factor(df_scaled.values, i) 
                       for i in range(len(feature_cols))]
    
    return vif_data.sort_values('VIF', ascending=False)

def analyze_correlations_and_vif():
    print("Korelasyon ve VIF analizi başlıyor...")
    
    # Veriyi yükle
    train_df = pd.read_csv('train_outlier_handled.csv')
    session_stats = pd.read_csv('session_stats_outlier_handled.csv', index_col=0)
    
    print(f"Train veri boyutu: {train_df.shape}")
    print(f"Session stats boyutu: {session_stats.shape}")
    
    # Temel feature'ları oluştur
    print("\n=== TEMEL FEATURE'LAR OLUŞTURULUYOR ===")
    
    # 1. Session bazlı feature'lar
    train_df['event_time'] = pd.to_datetime(train_df['event_time'])
    
    session_features = train_df.groupby('user_session').agg({
        'event_time': ['count', 'min', 'max'],
        'event_type': [lambda x: len(set(x)), lambda x: list(x)],
        'product_id': 'nunique',
        'category_id': 'nunique',
        'user_id': 'first',
        'session_value': 'first',
        'session_value_transformed': 'first'
    })
    
    # Sütun isimlerini düzenle
    session_features.columns = ['event_count', 'session_start', 'session_end', 
                               'unique_event_types', 'event_list', 'unique_products', 
                               'unique_categories', 'user_id', 'session_value', 
                               'session_value_transformed']
    
    # Session süresi (dakika)
    session_features['session_duration_minutes'] = (
        session_features['session_end'] - session_features['session_start']
    ).dt.total_seconds() / 60
    
    # Zaman bazlı feature'lar
    session_features['hour'] = session_features['session_start'].dt.hour
    session_features['day_of_week'] = session_features['session_start'].dt.dayofweek
    session_features['is_weekend'] = (session_features['day_of_week'] >= 5).astype(int)
    
    # Event türü bazlı feature'lar
    def count_event_type(event_list, event_type):
        return event_list.count(event_type)
    
    session_features['view_count'] = session_features['event_list'].apply(
        lambda x: count_event_type(x, 'VIEW'))
    session_features['add_cart_count'] = session_features['event_list'].apply(
        lambda x: count_event_type(x, 'ADD_CART'))
    session_features['remove_cart_count'] = session_features['event_list'].apply(
        lambda x: count_event_type(x, 'REMOVE_CART'))
    session_features['buy_count'] = session_features['event_list'].apply(
        lambda x: count_event_type(x, 'BUY'))
    
    # Boolean feature'lar
    session_features['has_view'] = (session_features['view_count'] > 0).astype(int)
    session_features['has_add_cart'] = (session_features['add_cart_count'] > 0).astype(int)
    session_features['has_remove_cart'] = (session_features['remove_cart_count'] > 0).astype(int)
    session_features['has_buy'] = (session_features['buy_count'] > 0).astype(int)
    
    # Ratio feature'lar
    session_features['buy_ratio'] = session_features['buy_count'] / session_features['event_count']
    session_features['add_cart_ratio'] = session_features['add_cart_count'] / session_features['event_count']
    session_features['view_ratio'] = session_features['view_count'] / session_features['event_count']
    session_features['remove_cart_ratio'] = session_features['remove_cart_count'] / session_features['event_count']
    
    # 2. User bazlı feature'lar
    print("User bazlı feature'lar oluşturuluyor...")
    
    user_features = train_df.groupby('user_id').agg({
        'user_session': 'nunique',
        'event_time': ['count', 'min', 'max'],
        'product_id': 'nunique',
        'category_id': 'nunique',
        'session_value': ['sum', 'mean', 'std', 'count']
    })
    
    user_features.columns = ['user_session_count', 'user_total_events', 'user_first_activity', 
                            'user_last_activity', 'user_unique_products', 'user_unique_categories',
                            'user_total_session_value', 'user_avg_session_value', 
                            'user_session_value_std', 'user_session_count_check']
    
    # User aktivite süresi
    user_features['user_activity_span_days'] = (
        user_features['user_last_activity'] - user_features['user_first_activity']
    ).dt.total_seconds() / (24 * 3600)
    
    # User segmentleri
    user_features['user_segment'] = pd.cut(user_features['user_session_count'], 
                                          bins=[0, 1, 3, 10, float('inf')], 
                                          labels=['single', 'low', 'medium', 'high'])
    
    # Session features'a user features'ı merge et
    final_features = session_features.merge(user_features, left_on='user_id', right_index=True)
    
    # NaN değerleri doldur
    final_features['session_duration_minutes'] = final_features['session_duration_minutes'].fillna(0)
    final_features['user_session_value_std'] = final_features['user_session_value_std'].fillna(0)
    final_features['user_activity_span_days'] = final_features['user_activity_span_days'].fillna(0)
    
    print(f"Final feature matrix boyutu: {final_features.shape}")
    
    # 3. Korelasyon analizi
    print("\n=== KORELASYON ANALİZİ ===")
    
    # Numerik feature'ları seç
    numeric_features = [
        'event_count', 'session_duration_minutes', 'unique_event_types', 
        'unique_products', 'unique_categories', 'hour', 'day_of_week',
        'view_count', 'add_cart_count', 'remove_cart_count', 'buy_count',
        'has_view', 'has_add_cart', 'has_remove_cart', 'has_buy',
        'buy_ratio', 'add_cart_ratio', 'view_ratio', 'remove_cart_ratio',
        'user_session_count', 'user_total_events', 'user_unique_products',
        'user_unique_categories', 'user_activity_span_days'
    ]
    
    # Target ile korelasyonları hesapla
    correlations = final_features[numeric_features + ['session_value']].corr()['session_value'].drop('session_value')
    correlations_sorted = correlations.abs().sort_values(ascending=False)
    
    print("Session value ile en yüksek korelasyonlu feature'lar:")
    print(correlations_sorted.head(15))
    
    # 4. VIF analizi
    print("\n=== VIF ANALİZİ ===")
    
    # En yüksek korelasyonlu feature'ları seç (VIF için)
    top_features = correlations_sorted.head(15).index.tolist()
    
    # Infinite değerleri kontrol et ve temizle
    vif_data = final_features[top_features].copy()
    vif_data = vif_data.replace([np.inf, -np.inf], np.nan)
    vif_data = vif_data.fillna(0)
    
    # VIF hesapla
    vif_results = calculate_vif(vif_data, top_features)
    print("VIF değerleri (yüksekten düşüğe):")
    print(vif_results)
    
    # 5. Görselleştirme
    print("\n=== GÖRSELLEŞTİRME ===")
    
    # Figure oluştur
    fig, axes = plt.subplots(2, 2, figsize=(20, 16))
    
    # 1. Korelasyon heatmap (tüm feature'lar)
    correlation_matrix = final_features[numeric_features].corr()
    mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
    
    sns.heatmap(correlation_matrix, mask=mask, annot=False, cmap='coolwarm', 
                center=0, square=True, ax=axes[0,0])
    axes[0,0].set_title('Feature Korelasyon Heatmap', fontsize=14)
    
    # 2. Session value ile korelasyonlar (bar plot)
    top_15_corr = correlations_sorted.head(15)
    colors = ['red' if x < 0 else 'green' for x in correlations[top_15_corr.index]]
    
    axes[0,1].barh(range(len(top_15_corr)), correlations[top_15_corr.index], color=colors)
    axes[0,1].set_yticks(range(len(top_15_corr)))
    axes[0,1].set_yticklabels(top_15_corr.index, fontsize=10)
    axes[0,1].set_xlabel('Korelasyon')
    axes[0,1].set_title('Session Value ile En Yüksek Korelasyonlar', fontsize=14)
    axes[0,1].grid(True, alpha=0.3)
    
    # 3. VIF değerleri (bar plot)
    vif_plot_data = vif_results.head(10)
    colors_vif = ['red' if x > 10 else 'orange' if x > 5 else 'green' for x in vif_plot_data['VIF']]
    
    axes[1,0].barh(range(len(vif_plot_data)), vif_plot_data['VIF'], color=colors_vif)
    axes[1,0].set_yticks(range(len(vif_plot_data)))
    axes[1,0].set_yticklabels(vif_plot_data['Feature'], fontsize=10)
    axes[1,0].set_xlabel('VIF Değeri')
    axes[1,0].set_title('VIF Değerleri (Multicollinearity)', fontsize=14)
    axes[1,0].axvline(x=5, color='orange', linestyle='--', alpha=0.7, label='VIF=5')
    axes[1,0].axvline(x=10, color='red', linestyle='--', alpha=0.7, label='VIF=10')
    axes[1,0].legend()
    axes[1,0].grid(True, alpha=0.3)
    
    # 4. Top feature'lar ile session value scatter plot
    top_feature = correlations_sorted.index[0]
    axes[1,1].scatter(final_features[top_feature], final_features['session_value'], 
                     alpha=0.5, s=1)
    axes[1,1].set_xlabel(top_feature)
    axes[1,1].set_ylabel('Session Value')
    axes[1,1].set_title(f'En Güçlü Feature: {top_feature} vs Session Value', fontsize=14)
    
    plt.tight_layout()
    plt.savefig('correlation_vif_analysis.png', dpi=300, bbox_inches='tight')
    print("Grafik 'correlation_vif_analysis.png' olarak kaydedildi.")
    
    # 6. Sonuçları kaydet
    print("\n=== SONUÇLARI KAYDETME ===")
    
    # Korelasyon sonuçları
    correlation_results = pd.DataFrame({
        'feature': correlations_sorted.index,
        'correlation_abs': correlations_sorted.values,
        'correlation': correlations[correlations_sorted.index].values
    })
    correlation_results.to_csv('feature_correlations.csv', index=False)
    
    # VIF sonuçları
    vif_results.to_csv('feature_vif.csv', index=False)
    
    # Feature matrix
    final_features.to_csv('features_with_correlations.csv')
    
    print("Sonuçlar kaydedildi:")
    print("- feature_correlations.csv: Korelasyon sonuçları")
    print("- feature_vif.csv: VIF sonuçları") 
    print("- features_with_correlations.csv: Tüm feature'lar")
    
    # 7. Özet ve öneriler
    print(f"\n{'='*60}")
    print("KORELASYON ve VIF ANALİZİ ÖZET")
    print(f"{'='*60}")
    
    print(f"\n🏆 EN GÜÇLÜ FEATURE'LAR (Korelasyon):")
    for i, (feature, corr) in enumerate(correlations_sorted.head(5).items()):
        print(f"{i+1}. {feature}: {correlations[feature]:.4f}")
    
    print(f"\n⚠️  YÜKSEK VIF FEATURE'LAR (>10):")
    high_vif = vif_results[vif_results['VIF'] > 10]
    if len(high_vif) > 0:
        for _, row in high_vif.iterrows():
            print(f"- {row['Feature']}: {row['VIF']:.2f}")
    else:
        print("Yüksek VIF feature yok!")
    
    print(f"\n📊 ÖNERİLER:")
    print("1. En güçlü feature'ları kullan")
    print("2. VIF > 10 olan feature'ları dikkatli kullan")
    print("3. Ratio feature'lar güçlü görünüyor")
    print("4. Event count tabanlı feature'lar önemli")
    
    return final_features, correlation_results, vif_results

if __name__ == "__main__":
    features, correlations, vif = analyze_correlations_and_vif()

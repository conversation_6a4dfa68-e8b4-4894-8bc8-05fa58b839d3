import pandas as pd
import numpy as np
from sklearn.preprocessing import QuantileTransformer, RobustScaler
from scipy import stats
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

class OutlierHandler:
    def __init__(self):
        self.log_transform = True
        self.capping_percentile = 95
        self.quantile_transformer = None
        self.robust_scaler = None
        self.capping_threshold = None
        
    def analyze_distribution(self, values, name="values"):
        """Dağılım analizi yapar"""
        print(f"\n=== {name.upper()} DAĞILIM ANALİZİ ===")
        print(f"Count: {len(values)}")
        print(f"Mean: {values.mean():.2f}")
        print(f"Median: {values.median():.2f}")
        print(f"Std: {values.std():.2f}")
        print(f"Skewness: {stats.skew(values):.3f}")
        print(f"Min: {values.min():.2f}, Max: {values.max():.2f}")
        
        # Percentile analizi
        percentiles = [90, 95, 99, 99.5]
        for p in percentiles:
            val = values.quantile(p/100)
            count = len(values[values > val])
            print(f"{p}. percentile: {val:.2f} - Üstü: {count} ({count/len(values)*100:.1f}%)")
    
    def apply_log_transform(self, values):
        """Log transformation uygular"""
        # Negatif değerler için offset ekle
        min_val = values.min()
        if min_val <= 0:
            offset = abs(min_val) + 1
            transformed = np.log1p(values + offset)
        else:
            transformed = np.log1p(values)
        
        print(f"Log transform - Skewness: {stats.skew(values):.3f} → {stats.skew(transformed):.3f}")
        return transformed
    
    def apply_capping(self, values, percentile=95):
        """Percentile capping uygular"""
        threshold = values.quantile(percentile/100)
        capped = values.clip(upper=threshold)
        
        affected = len(values[values > threshold])
        print(f"Capping ({percentile}. percentile = {threshold:.2f}) - Etkilenen: {affected} ({affected/len(values)*100:.1f}%)")
        
        return capped, threshold
    
    def apply_winsorizing(self, values, limits=(0.05, 0.05)):
        """Winsorizing uygular"""
        from scipy.stats import mstats
        winsorized = mstats.winsorize(values, limits=limits)
        
        lower_limit = values.quantile(limits[0])
        upper_limit = values.quantile(1 - limits[1])
        
        affected_lower = len(values[values < lower_limit])
        affected_upper = len(values[values > upper_limit])
        
        print(f"Winsorizing - Alt: {affected_lower}, Üst: {affected_upper} değer etkilendi")
        return winsorized
    
    def apply_quantile_transform(self, values):
        """Quantile transformation uygular"""
        qt = QuantileTransformer(output_distribution='normal', random_state=42)
        transformed = qt.fit_transform(values.values.reshape(-1, 1)).flatten()
        
        print(f"Quantile transform - Skewness: {stats.skew(values):.3f} → {stats.skew(transformed):.3f}")
        return transformed, qt
    
    def compare_methods(self, values, name="session_value"):
        """Farklı outlier handling yöntemlerini karşılaştırır"""
        print(f"\n{'='*60}")
        print(f"OUTLIER HANDLING YÖNTEMLERİ KARŞILAŞTIRMASI - {name.upper()}")
        print(f"{'='*60}")
        
        # Orijinal dağılım
        self.analyze_distribution(values, "Orijinal")
        
        methods_results = {}
        
        # 1. Log Transform
        log_transformed = self.apply_log_transform(values)
        methods_results['log_transform'] = log_transformed
        
        # 2. Capping (95. percentile)
        capped_95, threshold_95 = self.apply_capping(values, 95)
        methods_results['capped_95'] = capped_95
        
        # 3. Capping (99. percentile)
        capped_99, threshold_99 = self.apply_capping(values, 99)
        methods_results['capped_99'] = capped_99
        
        # 4. Winsorizing
        winsorized = self.apply_winsorizing(values)
        methods_results['winsorized'] = winsorized
        
        # 5. Quantile Transform
        quantile_transformed, qt = self.apply_quantile_transform(values)
        methods_results['quantile_transform'] = quantile_transformed
        
        # 6. Hibrit: Log + Capping
        log_then_capped, _ = self.apply_capping(log_transformed, 95)
        methods_results['log_then_capped'] = log_then_capped
        
        # 7. Hibrit: Capping + Log
        capped_then_log = self.apply_log_transform(capped_95)
        methods_results['capped_then_log'] = capped_then_log
        
        return methods_results
    
    def fit_best_method(self, values, method='hybrid_log_cap'):
        """En iyi yöntemi fit eder"""
        print(f"\n=== EN İYİ YÖNTEM FIT EDİLİYOR: {method.upper()} ===")
        
        if method == 'hybrid_log_cap':
            # 1. Log transform
            self.log_transformed = self.apply_log_transform(values)
            
            # 2. Capping threshold hesapla
            self.capping_threshold = self.log_transformed.quantile(0.95)
            
            # 3. Final transform
            final_values = self.log_transformed.clip(upper=self.capping_threshold)
            
            print(f"Final skewness: {stats.skew(final_values):.3f}")
            print(f"Final std: {final_values.std():.3f}")
            
            return final_values
        
        elif method == 'quantile_transform':
            self.quantile_transformer = QuantileTransformer(output_distribution='normal', random_state=42)
            transformed = self.quantile_transformer.fit_transform(values.values.reshape(-1, 1)).flatten()
            return transformed
        
        elif method == 'robust_scaling':
            # Önce capping sonra robust scaling
            capped, self.capping_threshold = self.apply_capping(values, 95)
            self.robust_scaler = RobustScaler()
            scaled = self.robust_scaler.fit_transform(capped.values.reshape(-1, 1)).flatten()
            return scaled
    
    def transform(self, values, method='hybrid_log_cap'):
        """Fit edilmiş yöntemi yeni verilere uygular"""
        if method == 'hybrid_log_cap':
            # 1. Log transform
            min_val = values.min()
            if min_val <= 0:
                offset = abs(min_val) + 1
                log_values = np.log1p(values + offset)
            else:
                log_values = np.log1p(values)
            
            # 2. Capping
            final_values = log_values.clip(upper=self.capping_threshold)
            return final_values
        
        elif method == 'quantile_transform':
            return self.quantile_transformer.transform(values.values.reshape(-1, 1)).flatten()
        
        elif method == 'robust_scaling':
            capped = values.clip(upper=self.capping_threshold)
            return self.robust_scaler.transform(capped.values.reshape(-1, 1)).flatten()

def main():
    # Veri yükle
    print("Veri yükleniyor...")
    train_df = pd.read_csv('train.csv')
    
    # Session bazında analiz
    session_stats = train_df.groupby('user_session').agg({
        'session_value': 'first',
        'event_time': 'count'
    }).rename(columns={'event_time': 'event_count'})
    
    # Outlier handler oluştur
    outlier_handler = OutlierHandler()
    
    # Farklı yöntemleri karşılaştır
    methods_results = outlier_handler.compare_methods(session_stats['session_value'])
    
    # Görselleştirme
    print(f"\n=== GÖRSELLEŞTİRME ===")
    fig, axes = plt.subplots(3, 3, figsize=(15, 12))
    axes = axes.flatten()
    
    # Orijinal
    axes[0].hist(session_stats['session_value'], bins=50, alpha=0.7, edgecolor='black')
    axes[0].set_title('Orijinal')
    axes[0].set_xlabel('Session Value')
    
    # Diğer yöntemler
    titles = ['Log Transform', 'Capped 95%', 'Capped 99%', 'Winsorized', 
              'Quantile Transform', 'Log→Cap', 'Cap→Log', 'Event Count']
    
    for i, (method, values) in enumerate(list(methods_results.items())[:7]):
        axes[i+1].hist(values, bins=50, alpha=0.7, edgecolor='black')
        axes[i+1].set_title(titles[i])
    
    # Event count dağılımı da ekle
    axes[8].hist(session_stats['event_count'], bins=50, alpha=0.7, edgecolor='black', color='green')
    axes[8].set_title('Event Count')
    
    plt.tight_layout()
    plt.savefig('outlier_handling_comparison.png', dpi=300, bbox_inches='tight')
    print("Grafik 'outlier_handling_comparison.png' olarak kaydedildi.")
    
    # En iyi yöntemi seç ve uygula
    print(f"\n=== EN İYİ YÖNTEM SEÇİMİ ===")
    print("Hibrit yaklaşım seçildi: Log Transform + 95% Capping")
    
    # Train için uygula
    best_transformed = outlier_handler.fit_best_method(session_stats['session_value'], 'hybrid_log_cap')
    
    # Sonuçları kaydet
    session_stats['session_value_original'] = session_stats['session_value']
    session_stats['session_value_transformed'] = best_transformed
    
    # Train dataframe'e merge et
    train_processed = train_df.merge(
        session_stats[['session_value_transformed']], 
        left_on='user_session', 
        right_index=True
    )
    
    # Kaydet
    train_processed.to_csv('train_outlier_handled.csv', index=False)
    session_stats.to_csv('session_stats_outlier_handled.csv')
    
    print(f"\nİşlenmiş veriler kaydedildi:")
    print(f"- train_outlier_handled.csv: {len(train_processed)} satır")
    print(f"- session_stats_outlier_handled.csv: {len(session_stats)} satır")
    
    # Final karşılaştırma
    print(f"\n=== FİNAL KARŞILAŞTIRMA ===")
    print(f"Orijinal session_value:")
    print(f"  Mean: {session_stats['session_value_original'].mean():.2f}")
    print(f"  Std: {session_stats['session_value_original'].std():.2f}")
    print(f"  Skewness: {stats.skew(session_stats['session_value_original']):.3f}")
    
    print(f"\nTransform edilmiş session_value:")
    print(f"  Mean: {session_stats['session_value_transformed'].mean():.2f}")
    print(f"  Std: {session_stats['session_value_transformed'].std():.2f}")
    print(f"  Skewness: {stats.skew(session_stats['session_value_transformed']):.3f}")
    
    return outlier_handler

if __name__ == "__main__":
    handler = main()

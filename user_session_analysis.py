import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Veri setini yükle
print("Veri seti yükleniyor...")
train_df = pd.read_csv('train.csv')
train_df['event_time'] = pd.to_datetime(train_df['event_time'])

print(f"Toplam event sayısı: {len(train_df)}")
print(f"Benzersiz user sayısı: {train_df['user_id'].nunique()}")
print(f"Benzersiz session sayısı: {train_df['user_session'].nunique()}")

# 1. SESSION BAZINDA ANALİZ
print("\n" + "="*60)
print("1. SESSION BAZINDA ANALİZ")
print("="*60)

session_analysis = train_df.groupby('user_session').agg({
    'event_time': ['count', 'min', 'max'],
    'event_type': lambda x: list(x),
    'product_id': 'nunique',
    'category_id': 'nunique',
    'user_id': 'first',
    'session_value': 'first'
}).round(2)

# Sütun isimlerini düzenle
session_analysis.columns = ['event_count', 'session_start', 'session_end', 'event_types', 
                           'unique_products', 'unique_categories', 'user_id', 'session_value']

# Session süresi hesapla (dakika)
session_analysis['session_duration_minutes'] = (
    session_analysis['session_end'] - session_analysis['session_start']
).dt.total_seconds() / 60

print("Session başına event sayısı dağılımı:")
event_count_dist = session_analysis['event_count'].value_counts().sort_index()
print(event_count_dist.head(10))

print(f"\nSession event count istatistikleri:")
print(session_analysis['event_count'].describe())

print(f"\nSession süresi istatistikleri (dakika):")
print(session_analysis['session_duration_minutes'].describe())

# Event türü çeşitliliği analizi
def count_unique_events(event_list):
    return len(set(event_list))

session_analysis['unique_event_types'] = session_analysis['event_types'].apply(count_unique_events)
print(f"\nSession başına benzersiz event türü sayısı:")
print(session_analysis['unique_event_types'].value_counts().sort_index())

# 2. USER BAZINDA ANALİZ
print("\n" + "="*60)
print("2. USER BAZINDA ANALİZ")
print("="*60)

user_analysis = train_df.groupby('user_id').agg({
    'user_session': 'nunique',
    'event_time': ['count', 'min', 'max'],
    'event_type': lambda x: list(x),
    'product_id': 'nunique',
    'category_id': 'nunique',
    'session_value': ['sum', 'mean', 'count']
}).round(2)

# Sütun isimlerini düzenle
user_analysis.columns = ['session_count', 'total_events', 'first_activity', 'last_activity',
                        'all_event_types', 'unique_products', 'unique_categories', 
                        'total_session_value', 'avg_session_value', 'session_value_count']

# User aktivite süresi hesapla (gün)
user_analysis['activity_span_days'] = (
    user_analysis['last_activity'] - user_analysis['first_activity']
).dt.total_seconds() / (24 * 3600)

print("User başına session sayısı dağılımı:")
session_count_dist = user_analysis['session_count'].value_counts().sort_index()
print(session_count_dist.head(10))

print(f"\nUser session count istatistikleri:")
print(user_analysis['session_count'].describe())

print(f"\nUser total events istatistikleri:")
print(user_analysis['total_events'].describe())

print(f"\nUser aktivite süresi (gün) istatistikleri:")
print(user_analysis['activity_span_days'].describe())

# 3. USER-SESSION İLİŞKİSİ ANALİZİ
print("\n" + "="*60)
print("3. USER-SESSION İLİŞKİSİ ANALİZİ")
print("="*60)

# Session analysis'e user bilgilerini ekle
session_with_user = session_analysis.merge(
    user_analysis[['session_count', 'total_events', 'activity_span_days']], 
    left_on='user_id', 
    right_index=True
)

print("Session value ile user session count korelasyonu:")
correlation = session_with_user['session_value'].corr(session_with_user['session_count'])
print(f"Korelasyon: {correlation:.4f}")

print("\nSession value ile session event count korelasyonu:")
correlation2 = session_with_user['session_value'].corr(session_with_user['event_count'])
print(f"Korelasyon: {correlation2:.4f}")

# 4. SEGMENT ANALİZİ
print("\n" + "="*60)
print("4. SEGMENT ANALİZİ")
print("="*60)

# User segmentleri
def categorize_user_activity(row):
    if row['session_count'] == 1:
        return 'Single Session User'
    elif row['session_count'] <= 3:
        return 'Low Activity User'
    elif row['session_count'] <= 10:
        return 'Medium Activity User'
    else:
        return 'High Activity User'

user_analysis['user_segment'] = user_analysis.apply(categorize_user_activity, axis=1)

print("User segmentleri:")
user_segments = user_analysis['user_segment'].value_counts()
print(user_segments)

# Session segmentleri
def categorize_session_activity(row):
    if row['event_count'] == 1:
        return 'Single Event Session'
    elif row['event_count'] <= 3:
        return 'Low Activity Session'
    elif row['event_count'] <= 10:
        return 'Medium Activity Session'
    else:
        return 'High Activity Session'

session_analysis['session_segment'] = session_analysis.apply(categorize_session_activity, axis=1)

print("\nSession segmentleri:")
session_segments = session_analysis['session_segment'].value_counts()
print(session_segments)

# 5. SESSION VALUE İLE İLİŞKİ ANALİZİ
print("\n" + "="*60)
print("5. SESSION VALUE İLE İLİŞKİ ANALİZİ")
print("="*60)

# Session segment bazında session value
print("Session segment bazında ortalama session value:")
segment_value = session_analysis.groupby('session_segment')['session_value'].agg(['count', 'mean', 'std', 'median']).round(2)
print(segment_value)

# User segment bazında session value (user_analysis'e session_analysis'i merge et)
user_session_merge = user_analysis.reset_index().merge(
    train_df.groupby('user_id')['session_value'].agg(['mean', 'sum']).reset_index(),
    on='user_id'
)

print("\nUser segment bazında ortalama session value:")
user_segment_value = user_session_merge.groupby('user_segment')['mean'].agg(['count', 'mean', 'std']).round(2)
print(user_segment_value)

# 6. EVENT PATTERN ANALİZİ
print("\n" + "="*60)
print("6. EVENT PATTERN ANALİZİ")
print("="*60)

# En yaygın event sequence'ları
def get_event_sequence(event_list):
    return ' -> '.join(event_list)

session_analysis['event_sequence'] = session_analysis['event_types'].apply(get_event_sequence)

print("En yaygın event sequence'ları (ilk 10):")
top_sequences = session_analysis['event_sequence'].value_counts().head(10)
for seq, count in top_sequences.items():
    avg_value = session_analysis[session_analysis['event_sequence'] == seq]['session_value'].mean()
    print(f"{seq}: {count} session, avg value: {avg_value:.2f}")

# 7. ZAMAN BAZLI ANALİZ
print("\n" + "="*60)
print("7. ZAMAN BAZLI ANALİZ")
print("="*60)

# Günlük aktivite
train_df['date'] = train_df['event_time'].dt.date
daily_stats = train_df.groupby('date').agg({
    'user_session': 'nunique',
    'user_id': 'nunique',
    'event_time': 'count',
    'session_value': 'mean'
}).round(2)

daily_stats.columns = ['daily_sessions', 'daily_users', 'daily_events', 'avg_session_value']

print("Günlük aktivite istatistikleri:")
print(daily_stats.describe())

# Saatlik aktivite
train_df['hour'] = train_df['event_time'].dt.hour
hourly_stats = train_df.groupby('hour').agg({
    'user_session': 'nunique',
    'session_value': 'mean'
}).round(2)

print(f"\nEn aktif saatler (session sayısına göre):")
top_hours = hourly_stats.sort_values('user_session', ascending=False).head(5)
print(top_hours)

# 8. ÖNEMLİ FEATURE'LAR
print("\n" + "="*60)
print("8. ÖNEMLİ FEATURE ÖZETLER")
print("="*60)

print("SESSION BAZLI FEATURE'LAR:")
print("- event_count: Session başına event sayısı")
print("- session_duration_minutes: Session süresi")
print("- unique_event_types: Benzersiz event türü sayısı")
print("- unique_products: Session'da görülen benzersiz ürün sayısı")
print("- unique_categories: Session'da görülen benzersiz kategori sayısı")

print("\nUSER BAZLI FEATURE'LAR:")
print("- session_count: User'ın toplam session sayısı")
print("- total_events: User'ın toplam event sayısı")
print("- activity_span_days: User'ın aktivite süresi (gün)")
print("- avg_session_value: User'ın ortalama session value'su")

print("\nSEGMENT BAZLI FEATURE'LAR:")
print("- user_segment: User aktivite segmenti")
print("- session_segment: Session aktivite segmenti")
print("- event_sequence: Session'daki event sırası")

# Görselleştirme için veri kaydet
print(f"\n=== VERİ KAYDETME ===")
session_analysis.to_csv('session_analysis.csv')
user_analysis.to_csv('user_analysis.csv')
print("session_analysis.csv ve user_analysis.csv dosyaları kaydedildi.")

print(f"\n=== SONUÇ ===")
print("✅ Session başına ortalama event sayısı: 2.0")
print("✅ User başına ortalama session sayısı: 1.4") 
print("✅ En yaygın: Single event session'lar (%66)")
print("✅ En yaygın: Single session user'lar (%73)")
print("✅ Session value ile event count arasında güçlü korelasyon var!")

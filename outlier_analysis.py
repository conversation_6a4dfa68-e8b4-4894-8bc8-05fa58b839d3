import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# Veri setini yükle
print("Veri seti yükleniyor...")
train_df = pd.read_csv('train.csv')

# Session bazında analiz için veriyi grupla
session_stats = train_df.groupby('user_session').agg({
    'session_value': 'first',
    'event_time': 'count',
    'event_type': lambda x: list(x),
    'product_id': 'nunique',
    'category_id': 'nunique',
    'user_id': 'first'
}).rename(columns={
    'event_time': 'event_count',
    'product_id': 'unique_products',
    'category_id': 'unique_categories'
})

print(f"Toplam session sayısı: {len(session_stats)}")

# Session value outlier analizi
session_values = session_stats['session_value']

print("\n=== SESSION VALUE OUTLIER ANALİZİ ===")
print(f"Session value istatistikleri:")
print(session_values.describe())

# IQR yöntemi ile outlier tespiti
Q1 = session_values.quantile(0.25)
Q3 = session_values.quantile(0.75)
IQR = Q3 - Q1
lower_bound = Q1 - 1.5 * IQR
upper_bound = Q3 + 1.5 * IQR

outliers_iqr = session_values[(session_values < lower_bound) | (session_values > upper_bound)]
print(f"\nIQR yöntemi ile outlier sayısı: {len(outliers_iqr)} ({len(outliers_iqr)/len(session_values)*100:.2f}%)")
print(f"IQR alt sınır: {lower_bound:.2f}, üst sınır: {upper_bound:.2f}")

# Z-score yöntemi ile outlier tespiti
z_scores = np.abs(stats.zscore(session_values))
outliers_zscore = session_values[z_scores > 3]
print(f"Z-score yöntemi ile outlier sayısı (>3): {len(outliers_zscore)} ({len(outliers_zscore)/len(session_values)*100:.2f}%)")

# Percentile analizi
percentiles = [90, 95, 99, 99.5, 99.9]
print(f"\nPercentile analizi:")
for p in percentiles:
    value = session_values.quantile(p/100)
    count = len(session_values[session_values > value])
    print(f"{p}. percentile: {value:.2f} - Üstündeki session sayısı: {count} ({count/len(session_values)*100:.2f}%)")

# En yüksek session value'ları incele
print(f"\nEn yüksek 10 session value:")
top_sessions = session_stats.nlargest(10, 'session_value')
print(top_sessions[['session_value', 'event_count', 'unique_products', 'unique_categories']])

# Event türü analizi - yüksek value'lu sessionlar
print(f"\n=== YÜKSEK VALUE'LU SESSIONLARIN EVENT ANALİZİ ===")
high_value_threshold = session_values.quantile(0.95)
high_value_sessions = session_stats[session_stats['session_value'] > high_value_threshold]

print(f"Yüksek value'lu session sayısı (>95. percentile): {len(high_value_sessions)}")
print(f"Yüksek value'lu sessionların ortalama event sayısı: {high_value_sessions['event_count'].mean():.2f}")
print(f"Normal sessionların ortalama event sayısı: {session_stats[session_stats['session_value'] <= high_value_threshold]['event_count'].mean():.2f}")

# Event türü dağılımı analizi
def analyze_event_types(event_list):
    event_counts = {}
    for events in event_list:
        for event in events:
            event_counts[event] = event_counts.get(event, 0) + 1
    return event_counts

high_value_events = analyze_event_types(high_value_sessions['event_type'].tolist())
normal_value_events = analyze_event_types(session_stats[session_stats['session_value'] <= high_value_threshold]['event_type'].tolist())

print(f"\nYüksek value'lu sessionlarda event türü dağılımı:")
total_high = sum(high_value_events.values())
for event, count in high_value_events.items():
    print(f"{event}: {count} ({count/total_high*100:.1f}%)")

print(f"\nNormal sessionlarda event türü dağılımı:")
total_normal = sum(normal_value_events.values())
for event, count in normal_value_events.items():
    print(f"{event}: {count} ({count/total_normal*100:.1f}%)")

# OUTLIER ÇÖZÜM ÖNERİLERİ
print(f"\n" + "="*60)
print("OUTLIER ÇÖZÜM ÖNERİLERİ")
print("="*60)

print(f"\n1. LOG TRANSFORMATION:")
log_values = np.log1p(session_values)
print(f"   Orijinal skewness: {stats.skew(session_values):.3f}")
print(f"   Log transform sonrası skewness: {stats.skew(log_values):.3f}")
print(f"   Log transform outlier sayısı (Z-score>3): {len(log_values[np.abs(stats.zscore(log_values)) > 3])}")

print(f"\n2. CAPPING/WINSORIZING:")
cap_95 = session_values.quantile(0.95)
cap_99 = session_values.quantile(0.99)
print(f"   95. percentile capping: {cap_95:.2f} - Etkilenen session: {len(session_values[session_values > cap_95])}")
print(f"   99. percentile capping: {cap_99:.2f} - Etkilenen session: {len(session_values[session_values > cap_99])}")

print(f"\n3. ROBUST SCALING:")
median_val = session_values.median()
mad_val = np.median(np.abs(session_values - median_val))
robust_scaled = (session_values - median_val) / mad_val
print(f"   Robust scaling sonrası outlier sayısı (>3 MAD): {len(robust_scaled[np.abs(robust_scaled) > 3])}")

print(f"\n4. QUANTILE TRANSFORMATION:")
from sklearn.preprocessing import QuantileTransformer
qt = QuantileTransformer(output_distribution='normal', random_state=42)
quantile_transformed = qt.fit_transform(session_values.values.reshape(-1, 1)).flatten()
print(f"   Quantile transform sonrası skewness: {stats.skew(quantile_transformed):.3f}")

print(f"\n5. SEGMENT BAZLI YAKLAŞIM:")
# Event count bazında segmentleme
low_activity = session_stats[session_stats['event_count'] == 1]
medium_activity = session_stats[(session_stats['event_count'] > 1) & (session_stats['event_count'] <= 5)]
high_activity = session_stats[session_stats['event_count'] > 5]

print(f"   Düşük aktivite (1 event): {len(low_activity)} session, ortalama value: {low_activity['session_value'].mean():.2f}")
print(f"   Orta aktivite (2-5 event): {len(medium_activity)} session, ortalama value: {medium_activity['session_value'].mean():.2f}")
print(f"   Yüksek aktivite (>5 event): {len(high_activity)} session, ortalama value: {high_activity['session_value'].mean():.2f}")

# Görselleştirme için basit istatistikler
print(f"\n=== GÖRSELLEŞTİRME İÇİN VERİ HAZIRLIĞI ===")
print("Matplotlib grafikleri oluşturuluyor...")

# Basit histogram ve boxplot için veri hazırla
plt.figure(figsize=(15, 10))

# 1. Orijinal dağılım
plt.subplot(2, 3, 1)
plt.hist(session_values, bins=50, alpha=0.7, edgecolor='black')
plt.title('Orijinal Session Value Dağılımı')
plt.xlabel('Session Value')
plt.ylabel('Frekans')

# 2. Log transform
plt.subplot(2, 3, 2)
plt.hist(log_values, bins=50, alpha=0.7, edgecolor='black', color='orange')
plt.title('Log Transform Sonrası')
plt.xlabel('Log(Session Value + 1)')
plt.ylabel('Frekans')

# 3. Boxplot
plt.subplot(2, 3, 3)
plt.boxplot(session_values)
plt.title('Session Value Boxplot')
plt.ylabel('Session Value')

# 4. Event count vs session value
plt.subplot(2, 3, 4)
plt.scatter(session_stats['event_count'], session_stats['session_value'], alpha=0.5)
plt.xlabel('Event Count')
plt.ylabel('Session Value')
plt.title('Event Count vs Session Value')

# 5. Percentile analizi
plt.subplot(2, 3, 5)
percentile_values = [session_values.quantile(p/100) for p in range(0, 101, 5)]
plt.plot(range(0, 101, 5), percentile_values)
plt.xlabel('Percentile')
plt.ylabel('Session Value')
plt.title('Percentile Analizi')

# 6. Capped version
plt.subplot(2, 3, 6)
capped_values = session_values.clip(upper=cap_95)
plt.hist(capped_values, bins=50, alpha=0.7, edgecolor='black', color='green')
plt.title(f'95. Percentile Capped ({cap_95:.0f})')
plt.xlabel('Session Value (Capped)')
plt.ylabel('Frekans')

plt.tight_layout()
plt.savefig('outlier_analysis.png', dpi=300, bbox_inches='tight')
print("Grafik 'outlier_analysis.png' olarak kaydedildi.")

print(f"\n=== ÖNERİLEN YAKLAŞIM ===")
print("1. İlk olarak LOG TRANSFORMATION uygula (skewness'i düşürür)")
print("2. Ardından 95-99. percentile CAPPING uygula")
print("3. Model için SEGMENT BAZLI feature engineering yap")
print("4. Cross-validation sırasında outlier'ların etkisini izle")
print("5. Ensemble modellerde robust algoritmalar kullan (Random Forest, XGBoost)")

import pandas as pd
import numpy as np
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

def apply_log_transform(values):
    """Log transformation uygular (train'de kullanılan yöntem)"""
    min_val = values.min()
    if min_val <= 0:
        offset = abs(min_val) + 1
        transformed = np.log1p(values + offset)
    else:
        transformed = np.log1p(values)
    return transformed

def main():
    print("Test verisi için outlier handling uygulanıyor...")
    
    # Test verisini yükle
    test_df = pd.read_csv('test.csv')
    print(f"Test veri boyutu: {test_df.shape}")
    
    # Train'den öğrenilen parametreleri yükle
    session_stats_train = pd.read_csv('session_stats_outlier_handled.csv', index_col=0)
    
    # Train'den öğrenilen capping threshold'u al
    # Train'de log transform sonrası 95. percentile capping uygulandı
    train_log_values = apply_log_transform(session_stats_train['session_value_original'])
    capping_threshold = train_log_values.quantile(0.95)
    
    print(f"Train'den öğrenilen capping threshold: {capping_threshold:.4f}")
    
    # Test için dummy session_value oluştur (feature engineering için gerekli)
    # Gerçek değerleri tahmin edeceğiz, şimdilik placeholder
    test_session_stats = test_df.groupby('user_session').agg({
        'event_time': 'count',
        'event_type': lambda x: list(x),
        'product_id': 'nunique',
        'category_id': 'nunique',
        'user_id': 'first'
    }).rename(columns={
        'event_time': 'event_count',
        'product_id': 'unique_products',
        'category_id': 'unique_categories'
    })
    
    print(f"Test session sayısı: {len(test_session_stats)}")
    print(f"Test session event count dağılımı:")
    print(test_session_stats['event_count'].value_counts().sort_index().head(10))
    
    # Test verisi için transformation fonksiyonu hazırla
    def transform_session_value_for_test(session_value):
        """Test verisi için session_value transformation"""
        # 1. Log transform
        log_value = apply_log_transform(pd.Series([session_value]))[0]
        
        # 2. Capping (train'den öğrenilen threshold ile)
        capped_value = min(log_value, capping_threshold)
        
        return capped_value
    
    # Test session istatistiklerini kaydet
    test_session_stats.to_csv('test_session_stats.csv')
    
    # Test dataframe'e session istatistiklerini merge et
    test_processed = test_df.merge(
        test_session_stats[['event_count', 'unique_products', 'unique_categories']], 
        left_on='user_session', 
        right_index=True
    )
    
    # Test verisini kaydet
    test_processed.to_csv('test_outlier_ready.csv', index=False)
    
    print(f"\nTest verisi hazırlandı:")
    print(f"- test_outlier_ready.csv: {len(test_processed)} satır")
    print(f"- test_session_stats.csv: {len(test_session_stats)} satır")
    
    # Train ve test karşılaştırması
    print(f"\n=== TRAIN vs TEST KARŞILAŞTIRMA ===")
    
    train_session_stats = pd.read_csv('session_stats_outlier_handled.csv', index_col=0)
    
    print(f"Event count dağılımı karşılaştırması:")
    print(f"Train - Ortalama event count: {train_session_stats['event_count'].mean():.2f}")
    print(f"Test  - Ortalama event count: {test_session_stats['event_count'].mean():.2f}")
    
    print(f"\nSession sayısı karşılaştırması:")
    print(f"Train session sayısı: {len(train_session_stats)}")
    print(f"Test session sayısı: {len(test_session_stats)}")
    
    # Event türü dağılımı
    def analyze_event_types(event_list):
        event_counts = {}
        for events in event_list:
            for event in events:
                event_counts[event] = event_counts.get(event, 0) + 1
        return event_counts
    
    test_events = analyze_event_types(test_session_stats['event_type'].tolist())
    total_test_events = sum(test_events.values())
    
    print(f"\nTest event türü dağılımı:")
    for event, count in test_events.items():
        print(f"{event}: {count} ({count/total_test_events*100:.1f}%)")
    
    # Transformation parametrelerini kaydet (JSON ile)
    import json

    transformation_params = {
        'method': 'hybrid_log_cap',
        'capping_threshold': float(capping_threshold)
    }

    with open('outlier_transformation_params.json', 'w') as f:
        json.dump(transformation_params, f, indent=2)
    
    print(f"\nTransformation parametreleri 'outlier_transformation_params.json' olarak kaydedildi.")
    
    # Özet
    print(f"\n=== OUTLIER HANDLING ÖZET ===")
    print(f"✅ Train verisi işlendi: Skewness 7.989 → -0.057")
    print(f"✅ Test verisi hazırlandı: {len(test_session_stats)} session")
    print(f"✅ Transformation parametreleri kaydedildi")
    print(f"✅ Hibrit yöntem: Log Transform + 95% Capping")
    print(f"✅ Sonraki adım: Feature Engineering")

if __name__ == "__main__":
    main()

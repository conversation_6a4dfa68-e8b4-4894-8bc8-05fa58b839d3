import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.feature_selection import SelectKBest, f_regression
import warnings
warnings.filterwarnings('ignore')

class OptimizedFeatureEngineer:
    def __init__(self):
        self.label_encoders = {}
        self.scaler = StandardScaler()
        self.feature_selector = None
        
    def create_core_features(self, df, is_train=True):
        """Korelasyon analizine dayalı core feature'ları oluşturur"""
        print("Core feature'lar oluşturuluyor...")
        
        df = df.copy()
        df['event_time'] = pd.to_datetime(df['event_time'])
        
        # Session bazlı aggregation
        session_agg = df.groupby('user_session').agg({
            'event_time': ['count', 'min', 'max'],
            'event_type': lambda x: list(x),
            'product_id': 'nunique',
            'category_id': 'nunique',
            'user_id': 'first'
        })
        
        session_agg.columns = ['event_count', 'session_start', 'session_end', 
                              'event_list', 'unique_products', 'unique_categories', 'user_id']
        
        # 1. GÜÇLÜ FEATURE'LAR (Yüksek korelasyon, düşük VIF)
        
        # Event türü sayıları
        session_agg['buy_count'] = session_agg['event_list'].apply(
            lambda x: x.count('BUY'))
        session_agg['add_cart_count'] = session_agg['event_list'].apply(
            lambda x: x.count('ADD_CART'))
        session_agg['view_count'] = session_agg['event_list'].apply(
            lambda x: x.count('VIEW'))
        session_agg['remove_cart_count'] = session_agg['event_list'].apply(
            lambda x: x.count('REMOVE_CART'))
        
        # Boolean feature'lar (güçlü ve VIF düşük)
        session_agg['has_buy'] = (session_agg['buy_count'] > 0).astype(int)
        session_agg['has_add_cart'] = (session_agg['add_cart_count'] > 0).astype(int)
        session_agg['has_view'] = (session_agg['view_count'] > 0).astype(int)
        session_agg['has_remove_cart'] = (session_agg['remove_cart_count'] > 0).astype(int)
        
        # Ratio feature'lar (çok güçlü!)
        session_agg['buy_ratio'] = session_agg['buy_count'] / session_agg['event_count']
        session_agg['add_cart_ratio'] = session_agg['add_cart_count'] / session_agg['event_count']
        session_agg['view_ratio'] = session_agg['view_count'] / session_agg['event_count']
        session_agg['remove_cart_ratio'] = session_agg['remove_cart_count'] / session_agg['event_count']
        
        # Benzersiz event türü sayısı (VIF düşük)
        session_agg['unique_event_types'] = session_agg['event_list'].apply(
            lambda x: len(set(x)))
        
        # 2. VIF SORUNUNU ÇÖZEN FEATURE'LAR
        
        # Log transformations (VIF'i düşürür)
        session_agg['log_event_count'] = np.log1p(session_agg['event_count'])
        session_agg['log_unique_products'] = np.log1p(session_agg['unique_products'])
        session_agg['log_unique_categories'] = np.log1p(session_agg['unique_categories'])
        
        # Session süresi
        session_agg['session_duration_minutes'] = (
            session_agg['session_end'] - session_agg['session_start']
        ).dt.total_seconds() / 60
        session_agg['session_duration_minutes'] = session_agg['session_duration_minutes'].fillna(0)
        session_agg['log_session_duration'] = np.log1p(session_agg['session_duration_minutes'])
        
        # 3. ZAMAN BAZLI FEATURE'LAR
        session_agg['hour'] = session_agg['session_start'].dt.hour
        session_agg['day_of_week'] = session_agg['session_start'].dt.dayofweek
        session_agg['is_weekend'] = (session_agg['day_of_week'] >= 5).astype(int)
        session_agg['is_business_hours'] = ((session_agg['hour'] >= 9) & 
                                           (session_agg['hour'] <= 17)).astype(int)
        
        # Peak hours (analiz sonuçlarına göre)
        peak_hours = [10, 11, 12, 18, 19]
        session_agg['is_peak_hour'] = session_agg['hour'].isin(peak_hours).astype(int)
        
        # 4. INTERACTION FEATURE'LAR (Güçlü feature'ların kombinasyonu)
        session_agg['buy_product_interaction'] = (session_agg['buy_count'] * 
                                                 session_agg['unique_products'])
        session_agg['buy_event_interaction'] = (session_agg['buy_count'] * 
                                               session_agg['event_count'])
        session_agg['product_diversity'] = (session_agg['unique_products'] / 
                                           session_agg['event_count'])
        session_agg['category_diversity'] = (session_agg['unique_categories'] / 
                                            session_agg['event_count'])
        
        # 5. BEHAVIORAL PATTERN FEATURE'LAR
        
        # Event sequence patterns
        def get_first_event(event_list):
            return event_list[0] if event_list else 'UNKNOWN'
        
        def get_last_event(event_list):
            return event_list[-1] if event_list else 'UNKNOWN'
        
        session_agg['first_event'] = session_agg['event_list'].apply(get_first_event)
        session_agg['last_event'] = session_agg['event_list'].apply(get_last_event)
        
        # Conversion patterns
        session_agg['starts_with_view'] = (session_agg['first_event'] == 'VIEW').astype(int)
        session_agg['ends_with_buy'] = (session_agg['last_event'] == 'BUY').astype(int)
        session_agg['view_to_buy'] = ((session_agg['has_view'] == 1) & 
                                     (session_agg['has_buy'] == 1)).astype(int)
        session_agg['cart_to_buy'] = ((session_agg['has_add_cart'] == 1) & 
                                     (session_agg['has_buy'] == 1)).astype(int)
        
        # 6. USER BAZLI FEATURE'LAR (VIF düşük olanlar)
        user_agg = df.groupby('user_id').agg({
            'user_session': 'nunique',
            'event_time': 'count',
            'product_id': 'nunique',
            'category_id': 'nunique'
        }).rename(columns={
            'user_session': 'user_session_count',
            'event_time': 'user_total_events',
            'product_id': 'user_unique_products_total',
            'category_id': 'user_unique_categories_total'
        })
        
        # User segment (VIF düşük)
        user_agg['user_activity_level'] = pd.cut(user_agg['user_session_count'], 
                                                 bins=[0, 1, 3, 10, float('inf')], 
                                                 labels=[0, 1, 2, 3])
        
        # Session features'a user features'ı merge et
        final_features = session_agg.merge(user_agg, left_on='user_id', right_index=True)
        
        # 7. ADVANCED FEATURE'LAR
        
        # Session efficiency metrics
        final_features['events_per_product'] = (final_features['event_count'] / 
                                               np.maximum(final_features['unique_products'], 1))
        final_features['events_per_category'] = (final_features['event_count'] / 
                                                np.maximum(final_features['unique_categories'], 1))
        
        # User context features
        final_features['session_vs_user_avg'] = (final_features['event_count'] / 
                                                np.maximum(final_features['user_total_events'] / 
                                                         final_features['user_session_count'], 1))
        
        # Polynomial features (en güçlü feature'lar için)
        final_features['buy_count_squared'] = final_features['buy_count'] ** 2
        final_features['buy_ratio_squared'] = final_features['buy_ratio'] ** 2
        
        return final_features
    
    def encode_categorical_features(self, df, categorical_cols, is_train=True):
        """Kategorik feature'ları encode eder"""
        df = df.copy()
        
        for col in categorical_cols:
            if col in df.columns:
                if is_train:
                    self.label_encoders[col] = LabelEncoder()
                    df[f'{col}_encoded'] = self.label_encoders[col].fit_transform(df[col].astype(str))
                else:
                    if col in self.label_encoders:
                        # Test'te görülmeyen kategoriler için
                        df[f'{col}_encoded'] = df[col].astype(str).map(
                            dict(zip(self.label_encoders[col].classes_, 
                                   self.label_encoders[col].transform(self.label_encoders[col].classes_)))
                        ).fillna(-1)
                    else:
                        df[f'{col}_encoded'] = -1
        
        return df
    
    def select_best_features(self, X, y, k=50):
        """En iyi feature'ları seçer"""
        print(f"En iyi {k} feature seçiliyor...")
        
        self.feature_selector = SelectKBest(score_func=f_regression, k=k)
        X_selected = self.feature_selector.fit_transform(X, y)
        
        # Seçilen feature isimlerini al
        selected_features = X.columns[self.feature_selector.get_support()].tolist()
        
        print(f"Seçilen feature'lar: {len(selected_features)}")
        return X_selected, selected_features
    
    def fit_transform_train(self, train_df):
        """Train verisi için feature engineering"""
        print("=== TRAIN VERİSİ FEATURE ENGİNEERİNG ===")
        
        # Core features oluştur
        features = self.create_core_features(train_df, is_train=True)
        
        # Kategorik feature'ları encode et
        categorical_cols = ['first_event', 'last_event', 'user_activity_level']
        features = self.encode_categorical_features(features, categorical_cols, is_train=True)
        
        # Target'ı al (train'de session_value var)
        if 'session_value' in train_df.columns:
            # Session value'yu merge et
            session_values = train_df.groupby('user_session')['session_value'].first()
            features = features.merge(session_values, left_index=True, right_index=True)
        
        # Numerik feature'ları seç
        numeric_features = features.select_dtypes(include=[np.number]).columns.tolist()
        
        # Target'ı çıkar
        if 'session_value' in numeric_features:
            numeric_features.remove('session_value')
        
        # NaN değerleri doldur
        features[numeric_features] = features[numeric_features].fillna(0)
        
        # Infinite değerleri temizle
        features[numeric_features] = features[numeric_features].replace([np.inf, -np.inf], 0)
        
        print(f"Toplam feature sayısı: {len(numeric_features)}")
        
        return features, numeric_features
    
    def transform_test(self, test_df, numeric_features):
        """Test verisi için feature engineering"""
        print("=== TEST VERİSİ FEATURE ENGİNEERİNG ===")
        
        # Core features oluştur
        features = self.create_core_features(test_df, is_train=False)
        
        # Kategorik feature'ları encode et
        categorical_cols = ['first_event', 'last_event', 'user_activity_level']
        features = self.encode_categorical_features(features, categorical_cols, is_train=False)
        
        # Aynı feature'ları seç
        missing_features = set(numeric_features) - set(features.columns)
        for feature in missing_features:
            features[feature] = 0
        
        # NaN değerleri doldur
        features[numeric_features] = features[numeric_features].fillna(0)
        
        # Infinite değerleri temizle
        features[numeric_features] = features[numeric_features].replace([np.inf, -np.inf], 0)
        
        return features

def main():
    print("Optimized Feature Engineering başlıyor...")
    
    # Veriyi yükle
    train_df = pd.read_csv('train_outlier_handled.csv')
    test_df = pd.read_csv('test_outlier_ready.csv')
    
    print(f"Train boyutu: {train_df.shape}")
    print(f"Test boyutu: {test_df.shape}")
    
    # Feature engineer oluştur
    fe = OptimizedFeatureEngineer()
    
    # Train features oluştur
    train_features, numeric_features = fe.fit_transform_train(train_df)
    
    # Test features oluştur
    test_features = fe.transform_test(test_df, numeric_features)
    
    # Feature'ları kaydet
    train_features.to_csv('train_features_optimized.csv')
    test_features.to_csv('test_features_optimized.csv')
    
    print(f"\n=== SONUÇLAR ===")
    print(f"Train features: {train_features.shape}")
    print(f"Test features: {test_features.shape}")
    print(f"Toplam feature sayısı: {len(numeric_features)}")
    
    # En önemli feature'ları göster
    if 'session_value' in train_features.columns:
        correlations = train_features[numeric_features + ['session_value']].corr()['session_value'].drop('session_value')
        top_features = correlations.abs().sort_values(ascending=False).head(10)
        
        print(f"\nEn güçlü 10 feature:")
        for i, (feature, corr) in enumerate(top_features.items()):
            print(f"{i+1}. {feature}: {corr:.4f}")
    
    print(f"\nDosyalar kaydedildi:")
    print(f"- train_features_optimized.csv")
    print(f"- test_features_optimized.csv")
    
    return train_features, test_features, numeric_features

if __name__ == "__main__":
    train_features, test_features, features = main()

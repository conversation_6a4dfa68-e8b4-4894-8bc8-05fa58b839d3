import pandas as pd
import numpy as np
import dtale
import warnings
warnings.filterwarnings('ignore')

# Veri setlerini yükle
print("Veri setleri yükleniyor...")
train_df = pd.read_csv('train.csv')
test_df = pd.read_csv('test.csv')
sample_submission = pd.read_csv('sample_submission.csv')

print(f"Train veri seti boyutu: {train_df.shape}")
print(f"Test veri seti boyutu: {test_df.shape}")
print(f"Sample submission boyutu: {sample_submission.shape}")

# Temel bilgiler
print("\n=== TRAIN VERİ SETİ BİLGİLERİ ===")
print(train_df.info())
print("\nİlk 5 satır:")
print(train_df.head())

print("\n=== TEST VERİ SETİ BİLGİLERİ ===")
print(test_df.info())
print("\nİlk 5 satır:")
print(test_df.head())

# Eksik değer kontrolü
print("\n=== EKSİK DEĞER ANALİZİ ===")
print("Train veri seti eksik değerler:")
print(train_df.isnull().sum())
print("\nTest veri seti eksik değerler:")
print(test_df.isnull().sum())

# Event türleri analizi
print("\n=== EVENT TÜRLERİ ANALİZİ ===")
print("Train veri setinde event türleri:")
print(train_df['event_type'].value_counts())
print("\nTest veri setinde event türleri:")
print(test_df['event_type'].value_counts())

# Session value istatistikleri
print("\n=== SESSION VALUE İSTATİSTİKLERİ ===")
print(train_df['session_value'].describe())

# Benzersiz değer sayıları
print("\n=== BENZERSİZ DEĞER SAYILARI ===")
print("Train veri seti:")
for col in train_df.columns:
    print(f"{col}: {train_df[col].nunique()}")

print("\nTest veri seti:")
for col in test_df.columns:
    print(f"{col}: {test_df[col].nunique()}")

# Tarih analizi
print("\n=== TARİH ANALİZİ ===")
train_df['event_time'] = pd.to_datetime(train_df['event_time'])
test_df['event_time'] = pd.to_datetime(test_df['event_time'])

print(f"Train veri seti tarih aralığı: {train_df['event_time'].min()} - {train_df['event_time'].max()}")
print(f"Test veri seti tarih aralığı: {test_df['event_time'].min()} - {test_df['event_time'].max()}")

# Session bazında analiz
print("\n=== SESSION BAZINDA ANALİZ ===")
train_session_stats = train_df.groupby('user_session').agg({
    'event_time': 'count',
    'session_value': 'first',
    'event_type': lambda x: x.nunique(),
    'product_id': lambda x: x.nunique(),
    'user_id': 'first'
}).rename(columns={
    'event_time': 'event_count',
    'event_type': 'unique_event_types',
    'product_id': 'unique_products'
})

print("Session başına istatistikler:")
print(train_session_stats.describe())

# D_tale ile görselleştirme başlat
print("\n=== D_TALE GÖRSELLEŞTİRME BAŞLATILIYOR ===")
print("Train veri seti için D_tale açılıyor...")

# Train veri seti için d_tale
d_train = dtale.show(train_df, host='localhost', port=40010)
print(f"Train veri seti D_tale URL'si: {d_train._url}")

# Session istatistikleri için d_tale
d_session = dtale.show(train_session_stats.reset_index(), host='localhost', port=40011)
print(f"Session istatistikleri D_tale URL'si: {d_session._url}")

# Test veri seti için d_tale
d_test = dtale.show(test_df, host='localhost', port=40012)
print(f"Test veri seti D_tale URL'si: {d_test._url}")

print("\nD_tale pencereleri açıldı. Tarayıcınızda yukarıdaki URL'leri ziyaret edebilirsiniz.")
print("Analizi durdurmak için Ctrl+C tuşlayın.")

# D_tale'i açık tutmak için
try:
    input("D_tale pencerelerini kapatmak için Enter tuşuna basın...")
except KeyboardInterrupt:
    print("\nD_tale kapatılıyor...")
